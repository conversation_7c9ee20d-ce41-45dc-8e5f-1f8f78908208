import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 设置matplotlib后端和字体
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data(filename):
    """加载数据集"""
    data = pd.read_csv(filename)
    heights = data['Height(Inches)'].values
    return heights

def split_data(data, train_ratio=0.8, random_seed=42):
    """划分训练集和测试集"""
    np.random.seed(random_seed)
    n = len(data)
    indices = np.random.permutation(n)
    train_size = int(n * train_ratio)
    
    train_indices = indices[:train_size]
    test_indices = indices[train_size:]
    
    train_data = data[train_indices]
    test_data = data[test_indices]
    
    return train_data, test_data

def moment_estimation(data):
    """矩估计法估计正态分布参数"""
    mu_hat = np.mean(data)
    sigma2_hat = np.var(data, ddof=0)  # 使用总体方差公式
    sigma_hat = np.sqrt(sigma2_hat)
    
    return mu_hat, sigma_hat

def mle_estimation(data):
    """极大似然估计法估计正态分布参数"""
    mu_hat = np.mean(data)
    sigma2_hat = np.var(data, ddof=0)  # MLE使用总体方差公式
    sigma_hat = np.sqrt(sigma2_hat)
    
    return mu_hat, sigma_hat

def normal_pdf(x, mu, sigma):
    """正态分布概率密度函数"""
    return (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2)

def log_likelihood(data, mu, sigma):
    """计算对数似然值"""
    n = len(data)
    ll = -n/2 * np.log(2 * np.pi) - n * np.log(sigma) - np.sum((data - mu)**2) / (2 * sigma**2)
    return ll

def evaluate_estimation(test_data, mu, sigma):
    """评估估计效果"""
    # 计算对数似然值
    ll = log_likelihood(test_data, mu, sigma)

    # 计算均方误差（使用经验分布与理论分布的差异）
    x_test = np.sort(test_data)
    empirical_cdf = np.arange(1, len(x_test) + 1) / len(x_test)

    # 手动实现标准正态分布CDF的近似
    def normal_cdf(x, mu, sigma):
        z = (x - mu) / sigma
        # 使用误差函数的近似公式
        def erf_approx(x):
            # Abramowitz and Stegun approximation
            a1 =  0.254829592
            a2 = -0.284496736
            a3 =  1.421413741
            a4 = -1.453152027
            a5 =  1.061405429
            p  =  0.3275911

            sign = np.sign(x)
            x = np.abs(x)

            t = 1.0 / (1.0 + p * x)
            y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * np.exp(-x * x)

            return sign * y

        return 0.5 * (1 + erf_approx(z / np.sqrt(2)))

    theoretical_cdf = normal_cdf(x_test, mu, sigma)
    mse = np.mean((empirical_cdf - theoretical_cdf) ** 2)

    return ll, mse

def plot_results(train_data, test_data, mu_moment, sigma_moment, mu_mle, sigma_mle):
    """绘制结果图像"""
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 训练数据直方图和估计的分布曲线
        ax1 = axes[0, 0]
        ax1.hist(train_data, bins=50, density=True, alpha=0.7, color='lightblue', label='Training Data Histogram')

        x_range = np.linspace(train_data.min() - 2, train_data.max() + 2, 1000)

        # 矩估计曲线
        y_moment = normal_pdf(x_range, mu_moment, sigma_moment)
        ax1.plot(x_range, y_moment, 'r-', linewidth=2, label=f'Method of Moments (mu={mu_moment:.2f}, sigma={sigma_moment:.2f})')

        # 极大似然估计曲线
        y_mle = normal_pdf(x_range, mu_mle, sigma_mle)
        ax1.plot(x_range, y_mle, 'g--', linewidth=2, label=f'MLE (mu={mu_mle:.2f}, sigma={sigma_mle:.2f})')

        ax1.set_xlabel('Height (Inches)')
        ax1.set_ylabel('Probability Density')
        ax1.set_title('Training Data Distribution and Estimated Curves')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    
        # 2. 测试数据直方图和估计的分布曲线
        ax2 = axes[0, 1]
        ax2.hist(test_data, bins=30, density=True, alpha=0.7, color='lightgreen', label='Test Data Histogram')

        x_test_range = np.linspace(test_data.min() - 2, test_data.max() + 2, 1000)
        y_moment_test = normal_pdf(x_test_range, mu_moment, sigma_moment)
        y_mle_test = normal_pdf(x_test_range, mu_mle, sigma_mle)

        ax2.plot(x_test_range, y_moment_test, 'r-', linewidth=2, label='Method of Moments')
        ax2.plot(x_test_range, y_mle_test, 'g--', linewidth=2, label='MLE')

        ax2.set_xlabel('Height (Inches)')
        ax2.set_ylabel('Probability Density')
        ax2.set_title('Test Data Distribution and Estimated Curves')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
        # 3. 简化的Q-Q图比较
        ax3 = axes[1, 0]
        test_sorted = np.sort(test_data)
        n_test = len(test_sorted)

        # 使用简单的分位数比较
        quantiles = np.linspace(0.1, 0.9, min(100, n_test//10))  # 减少点数以提高性能
        sample_quantiles = np.quantile(test_sorted, quantiles)

        # 理论分位数（使用简单的正态分布近似）
        z_scores = np.array([np.sqrt(2) * np.sqrt(-np.log(2*(1-q))) if q > 0.5
                            else -np.sqrt(2) * np.sqrt(-np.log(2*q)) for q in quantiles])

        moment_quantiles = mu_moment + sigma_moment * z_scores
        mle_quantiles = mu_mle + sigma_mle * z_scores

        ax3.scatter(moment_quantiles, sample_quantiles, alpha=0.6, s=20, color='red', label='Method of Moments')
        ax3.scatter(mle_quantiles, sample_quantiles, alpha=0.6, s=20, color='green', label='MLE')

        # 添加理想直线
        min_val = min(np.min(sample_quantiles), np.min(moment_quantiles), np.min(mle_quantiles))
        max_val = max(np.max(sample_quantiles), np.max(moment_quantiles), np.max(mle_quantiles))
        ax3.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, label='Ideal Line')

        ax3.set_xlabel('Theoretical Quantiles')
        ax3.set_ylabel('Sample Quantiles')
        ax3.set_title('Q-Q Plot: Prediction Accuracy Comparison')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
        # 4. 评估指标比较
        ax4 = axes[1, 1]

        # 计算评估指标
        ll_moment, mse_moment = evaluate_estimation(test_data, mu_moment, sigma_moment)
        ll_mle, mse_mle = evaluate_estimation(test_data, mu_mle, sigma_mle)

        methods = ['Method of Moments', 'MLE']
        log_likelihoods = [ll_moment, ll_mle]
        mses = [mse_moment, mse_mle]

        x_pos = np.arange(len(methods))

        # 创建双y轴
        ax4_twin = ax4.twinx()

        bars1 = ax4.bar(x_pos - 0.2, log_likelihoods, 0.4, label='Log Likelihood', color='skyblue')
        bars2 = ax4_twin.bar(x_pos + 0.2, mses, 0.4, label='MSE', color='orange')

        ax4.set_xlabel('Estimation Method')
        ax4.set_ylabel('Log Likelihood', color='blue')
        ax4_twin.set_ylabel('Mean Squared Error', color='orange')
        ax4.set_title('Estimation Method Evaluation Comparison')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels(methods)

        # 添加数值标签
        for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
            height1 = bar1.get_height()
            height2 = bar2.get_height()
            ax4.text(bar1.get_x() + bar1.get_width()/2., height1,
                    f'{height1:.2f}', ha='center', va='bottom')
            ax4_twin.text(bar2.get_x() + bar2.get_width()/2., height2,
                         f'{height2:.4f}', ha='center', va='bottom')

        ax4.legend(loc='upper left')
        ax4_twin.legend(loc='upper right')

        plt.tight_layout()
        plt.savefig('height_modeling_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        return ll_moment, mse_moment, ll_mle, mse_mle

    except Exception as e:
        print(f"Error in plotting: {e}")
        # 如果绘图失败，仍然返回评估结果
        ll_moment, mse_moment = evaluate_estimation(test_data, mu_moment, sigma_moment)
        ll_mle, mse_mle = evaluate_estimation(test_data, mu_mle, sigma_mle)
        return ll_moment, mse_moment, ll_mle, mse_mle

def main():
    """主函数"""
    print("=== 身高数据正态分布建模 ===\n")
    
    # 1. 加载数据
    print("1. 加载数据...")
    heights = load_data('SOCR-HeightWeightcsv')
    print(f"   数据总数: {len(heights)}")
    print(f"   身高范围: {heights.min():.2f} - {heights.max():.2f} 英寸")
    print(f"   平均身高: {heights.mean():.2f} 英寸")
    print(f"   身高标准差: {heights.std():.2f} 英寸\n")
    
    # 2. 划分数据集
    print("2. 划分训练集和测试集...")
    train_data, test_data = split_data(heights, train_ratio=0.8)
    print(f"   训练集大小: {len(train_data)}")
    print(f"   测试集大小: {len(test_data)}\n")
    
    # 3. 矩估计
    print("3. 矩估计...")
    mu_moment, sigma_moment = moment_estimation(train_data)
    print(f"   估计参数: μ = {mu_moment:.4f}, σ = {sigma_moment:.4f}\n")
    
    # 4. 极大似然估计
    print("4. 极大似然估计...")
    mu_mle, sigma_mle = mle_estimation(train_data)
    print(f"   估计参数: μ = {mu_mle:.4f}, σ = {sigma_mle:.4f}\n")
    
    # 5. 评估估计效果
    print("5. 在测试集上评估估计效果...")
    ll_moment, mse_moment, ll_mle, mse_mle = plot_results(
        train_data, test_data, mu_moment, sigma_moment, mu_mle, sigma_mle
    )
    
    print("   矩估计:")
    print(f"     对数似然值: {ll_moment:.4f}")
    print(f"     均方误差: {mse_moment:.6f}")
    
    print("   极大似然估计:")
    print(f"     对数似然值: {ll_mle:.4f}")
    print(f"     均方误差: {mse_mle:.6f}\n")
    
    # 6. 结论
    print("6. 结论:")
    if ll_mle > ll_moment:
        print("   极大似然估计在对数似然值上表现更好")
    else:
        print("   矩估计在对数似然值上表现更好")
        
    if mse_mle < mse_moment:
        print("   极大似然估计在均方误差上表现更好")
    else:
        print("   矩估计在均方误差上表现更好")
    
    print(f"\n   注意：对于正态分布，矩估计和极大似然估计的结果应该非常接近")
    print(f"   μ差异: {abs(mu_moment - mu_mle):.6f}")
    print(f"   σ差异: {abs(sigma_moment - sigma_mle):.6f}")

if __name__ == "__main__":
    main()
