#### 对身高和体重数据进行建模
我有一个csv格式数据集SOCR-HeightWeight.csv，部分数据如下:
```
Index,Height(Inches),Weight(Pounds)
1,65.78331,112.9925
2,71.51521,136.4873
3,69.39874,153.0269
4,68.2166,142.3354
```
Index为序号，Height为升高，Weight为体重.
<br>
现在我只考虑身高数据，假定身高服从正态分布,期望为$\miu$,方差为$\sigma^2$，数据是从这个分布中采样出来的.
请将给定数据集以合适的方案划分为训练集和测试集，分别使用矩估计和极大似然估计来估计这个正态分布，并使用测试集评估两个估计。
<br>
使用python实现，第三方库只能使用numpy,pandas,matplotlib，
要绘制出直方图和估计得正态分布曲线，以及预测正确性的图像.