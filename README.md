# 身高数据正态分布建模

## 项目概述

本项目实现了对身高数据的正态分布建模，使用矩估计和极大似然估计两种方法来估计正态分布的参数，并在测试集上评估两种方法的性能。

## 数据集

- **文件**: `SOCR-HeightWeightcsv`
- **数据量**: 25,000个样本
- **特征**: 身高（英寸）和体重（磅）
- **使用**: 仅使用身高数据进行建模

## 实现功能

### 1. 数据处理
- 加载CSV格式的身高体重数据
- 提取身高数据
- 按8:2比例划分训练集和测试集

### 2. 参数估计
- **矩估计法**: 使用样本均值和样本方差估计正态分布参数
- **极大似然估计法**: 通过最大化似然函数估计参数

### 3. 模型评估
- **对数似然值**: 衡量模型对数据的拟合程度
- **均方误差**: 比较经验分布与理论分布的差异

### 4. 可视化
生成四个子图：
1. 训练数据直方图与估计的正态分布曲线
2. 测试数据直方图与估计的正态分布曲线  
3. Q-Q图：比较样本分位数与理论分位数
4. 评估指标比较：对数似然值和均方误差

## 使用方法

```bash
python height_modeling.py
```

## 运行结果

程序会输出：
- 数据集基本统计信息
- 两种估计方法的参数结果
- 在测试集上的评估指标
- 保存可视化结果图像为 `height_modeling_results.png`

## 理论背景

### 矩估计法
使用样本矩来估计总体参数：
- μ̂ = X̄ (样本均值)
- σ̂² = (1/n)∑(Xi - X̄)² (样本方差)

### 极大似然估计法
通过最大化似然函数来估计参数：
- 对于正态分布，MLE与矩估计给出相同结果
- μ̂ = X̄
- σ̂² = (1/n)∑(Xi - X̄)²

## 依赖库

- numpy: 数值计算
- pandas: 数据处理
- matplotlib: 数据可视化

## 结果分析

对于正态分布，矩估计和极大似然估计在理论上应该给出相同的结果，这在实际运行中得到了验证。两种方法的参数估计值几乎完全相同，评估指标也基本一致。
